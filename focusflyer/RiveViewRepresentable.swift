//
//  RiveViewRepresentable.swift
//  focusflyer
//
//  Created by 张国豪 on 2025/7/1.
//

import SwiftUI
import RiveRuntime

/// 将 Rive 的 UIKit 视图 (`RiveView`) 封装为 SwiftUI 可用的 `View`
struct RiveViewRepresentable: UIViewRepresentable {
    /// 负责加载 `.riv` 文件并控制动画的 ViewModel
    let model: RiveViewModel

    func makeUIView(context: Context) -> RiveView {
        // 使用默认构造器创建 RiveView 容器
        let riveView = RiveView()
        // 绑定 ViewModel 与视图
        model.setView(riveView)
        return riveView
    }

    func updateUIView(_ uiView: RiveView, context: Context) {
        // 当前无需对状态变更做处理
    }
}

//
//  SimpleGodotKitView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//  基于官方示例的最简 SwiftGodotKit 实现
//  遵循 "There can only be one GodotApp in your application" 原则
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

/// 全局 GodotApp 单例
class GlobalGodotApp: ObservableObject {
    static let shared = GlobalGodotApp()
    
    @Published var app: GodotApp?
    @Published var isReady = false
    @Published var errorMessage: String?
    
    private var hasInitialized = false
    
    private init() {
        print("GlobalGodotApp: 初始化全局单例")
    }
    
    func initializeIfNeeded() {
        guard !hasInitialized else {
            print("GlobalGodotApp: 已经初始化过")
            return
        }
        
        hasInitialized = true
        
        print("GlobalGodotApp: 开始初始化唯一的 GodotApp")
        
        // 检查 PCK 文件
        guard Bundle.main.path(forResource: "test2", ofType: "pck") != nil else {
            print("GlobalGodotApp: PCK 文件未找到")
            DispatchQueue.main.async {
                self.errorMessage = "PCK 文件 'test2.pck' 未找到"
            }
            return
        }
        
        do {
            // 创建唯一的 GodotApp 实例
            let godotApp = GodotApp(packFile: "test2.pck")
            
            DispatchQueue.main.async {
                self.app = godotApp
                
                // 延迟设置就绪状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                    self.isReady = true
                    print("GlobalGodotApp: GodotApp 就绪")
                }
            }
            
        } catch {
            print("GlobalGodotApp: GodotApp 创建失败: \(error)")
            DispatchQueue.main.async {
                self.errorMessage = "GodotApp 创建失败: \(error.localizedDescription)"
            }
        }
    }
    
    func reset() {
        print("GlobalGodotApp: 重置（仅在严重错误时使用）")
        hasInitialized = false
        app = nil
        isReady = false
        errorMessage = nil
    }
}

/// 简单的 GodotKit 游戏视图
struct SimpleGodotKitView: View {
    @StateObject private var globalApp = GlobalGodotApp.shared
    @State private var isLoading = true
    @State private var showDebugInfo = false
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if let app = globalApp.app, globalApp.isReady {
                // Godot 游戏视图 - 完全按照官方示例
                VStack {
                    if showDebugInfo {
                        VStack {
                            Text("全局 GodotApp 运行中")
                                .font(.caption)
                                .foregroundColor(.green)
                            Button("隐藏调试") {
                                showDebugInfo = false
                            }
                            .font(.caption)
                        }
                        .padding(.top, 50)
                    }
                    
                    GodotAppView()
                        .background(Color.black)
                        .onTapGesture(count: 3) {
                            showDebugInfo.toggle()
                        }
                }
                .environment(\.godotApp, app)
                
            } else if let errorMessage = globalApp.errorMessage {
                // 错误视图
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("GodotApp 错误")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    
                    if showDebugInfo {
                        VStack(alignment: .leading, spacing: 5) {
                            Text("调试信息:")
                                .font(.headline)
                            Text("• App 实例: \(globalApp.app != nil ? "存在" : "无")")
                            Text("• 就绪状态: \(globalApp.isReady)")
                            Text("• 错误: \(errorMessage)")
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    }

                    HStack {
                        Button("重试") {
                            retryInitialization()
                        }
                        .buttonStyle(.bordered)
                        
                        Button("重置") {
                            resetGodotApp()
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.red)
                    }
                    
                    Button(showDebugInfo ? "隐藏调试" : "显示调试") {
                        showDebugInfo.toggle()
                    }
                    .font(.caption)
                }
                .padding()
                
            } else if isLoading {
                // 加载视图
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("初始化全局 GodotApp...")
                        .font(.title2)
                    Text("首次启动需要更长时间")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        closeView()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            setupGodotApp()
        }
    }
    
    private func setupGodotApp() {
        print("SimpleGodotKitView: 设置 GodotApp")
        
        isLoading = true
        
        // 初始化全局 GodotApp（如果还没有）
        globalApp.initializeIfNeeded()
        
        // 等待初始化完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.isLoading = false
        }
    }
    
    private func retryInitialization() {
        print("SimpleGodotKitView: 重试初始化")
        setupGodotApp()
    }
    
    private func resetGodotApp() {
        print("SimpleGodotKitView: 重置 GodotApp")
        globalApp.reset()
        setupGodotApp()
    }
    
    private func closeView() {
        print("SimpleGodotKitView: 关闭视图（保持 GodotApp 运行）")
        // 不销毁 GodotApp，只是关闭视图
        isPresented = false
    }
}

#Preview {
    SimpleGodotKitView(isPresented: .constant(true))
}

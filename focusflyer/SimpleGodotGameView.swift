//
//  SimpleGodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct SimpleGodotGameView: View {
    // Following the exact demo pattern
    @State var app: GodotApp?
    @State private var errorMessage: String?
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if let app = app {
                VStack {
                    Text("Godot Game is below:")
                        .foregroundColor(.white)
                        .padding(.top, 50)

                    GodotAppView()
                        .padding()
                        .background(Color.black)
                }
                .environment(\.godotApp, app)
            } else if let errorMessage = errorMessage {
                VStack(spacing: 20) {
                    Image(systemName: "gamecontroller")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("Godot Setup Issue")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Button("Try Again") {
                        loadGame()
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
            } else {
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("Loading Godot Game...")
                        .font(.title2)
                }
            }

            // Close button
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        app = nil
                        isPresented = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            loadGame()
        }
    }

    private func loadGame() {
        print("Loading Godot game...")
        errorMessage = nil

        // Check if PCK file exists
        guard let pckPath = Bundle.main.path(forResource: "test2", ofType: "pck") else {
            errorMessage = "PCK file 'test2.pck' not found in app bundle"
            return
        }

        let fileSize = getFileSize(path: pckPath)
        print("Found PCK file: \(pckPath), size: \(fileSize) bytes")

        if fileSize < 100 {
            errorMessage = """
            PCK file is too small (\(fileSize) bytes).

            Please create a proper Godot project:
            1. Open Godot Editor
            2. Create a new project with some content
            3. Export as PCK file for iOS
            4. Replace main.pck in the app bundle
            """
            return
        }

        // Create GodotApp following the demo pattern exactly
        do {
            let godotApp = GodotApp(packFile: "test2.pck")
            self.app = godotApp
            print("Successfully created GodotApp")
        } catch {
            errorMessage = "Failed to create GodotApp: \(error.localizedDescription)"
        }
    }

    private func getFileSize(path: String) -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
}

#Preview {
    SimpleGodotGameView(isPresented: .constant(true))
}

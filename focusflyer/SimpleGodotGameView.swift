//
//  SimpleGodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//  使用全局Godot管理器的游戏视图
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct ManagedGodotGameView: View {
    @StateObject private var godotManager = GlobalGodotManager.shared
    @Binding var isPresented: Bool
    @State private var showDebugInfo = false

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if let app = godotManager.currentApp, godotManager.isReady {
                // Godot游戏视图
                VStack {
                    if showDebugInfo {
                        VStack {
                            Text("Godot引擎状态")
                                .font(.caption)
                                .foregroundColor(.green)
                            Text(godotManager.getStatusInfo())
                                .font(.caption2)
                                .foregroundColor(.gray)
                                .multilineTextAlignment(.leading)
                            Button("隐藏调试") {
                                showDebugInfo = false
                            }
                            .font(.caption)
                        }
                        .padding(.top, 50)
                        .padding(.horizontal)
                    }

                    Text("Game is below:")
                        .foregroundColor(.white)
                        .padding(.top, showDebugInfo ? 10 : 50)

                    GodotAppView()
                        .padding()
                        .background(Color.black)
                        .onTapGesture(count: 3) {
                            showDebugInfo.toggle()
                        }
                }
                .environment(\.godotApp, app)

            } else if godotManager.isLoading {
                // 加载状态
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("正在加载Godot引擎...")
                        .font(.title2)
                        .foregroundColor(.white)
                }

            } else if let errorMessage = godotManager.errorMessage {
                // 错误状态
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("Godot引擎错误")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                        .foregroundColor(.gray)

                    Button("重试") {
                        retryInitialization()
                    }
                    .buttonStyle(.bordered)
                }
                .padding()

            } else {
                // 初始状态
                VStack(spacing: 20) {
                    Text("准备启动Godot引擎...")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }

            // 关闭按钮和控制按钮
            VStack {
                HStack {
                    // 重置按钮（调试用）
                    if showDebugInfo {
                        Button("重置引擎") {
                            resetEngine()
                        }
                        .font(.caption)
                        .buttonStyle(.bordered)
                    }

                    Spacer()

                    Button(action: {
                        closeGame()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .background(Color.black)
        .onAppear {
            print("ManagedGodotGameView: 显示游戏视图")
        }
        .onDisappear {
            print("ManagedGodotGameView: 隐藏游戏视图")
            godotManager.hideGame()
        }
    }

    private func retryInitialization() {
        print("ManagedGodotGameView: 重试初始化")
        godotManager.resetEngine()

        let gameData: [String: Any] = [
            "level": 1,
            "playerName": "Player1",
            "difficulty": "normal"
        ]

        _ = godotManager.getGodotApp(pckFile: "test2.pck", gameData: gameData)
    }

    private func resetEngine() {
        print("ManagedGodotGameView: 重置引擎")
        godotManager.resetEngine()
    }

    private func closeGame() {
        print("ManagedGodotGameView: 关闭游戏")
        godotManager.hideGame()
        isPresented = false
    }

}

#Preview {
    OfficialGodotGameView(
        godotApp: GodotApp(packFile: "test2.pck"),
        isPresented: .constant(true)
    )
}

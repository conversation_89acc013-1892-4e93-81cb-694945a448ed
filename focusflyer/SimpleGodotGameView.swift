//
//  SimpleGodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//  修改为官方推荐模式：接收外部 GodotApp 实例
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct OfficialGodotGameView: View {
    let godotApp: GodotApp  // 接收外部传入的 GodotApp 实例
    @Binding var isPresented: Bool

    init(godotApp: GodotApp, isPresented: Binding<Bool>) {
        self.godotApp = godotApp
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            // 完全按照官方示例的结构
            VStack {
                Text("Game is below:")
                    .foregroundColor(.white)
                    .padding(.top, 50)

                GodotAppView()
                    .padding()
                    .background(Color.black)
            }
            .environment(\.godotApp, godotApp)  // 使用传入的 GodotApp 实例

            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        // 只关闭视图，不销毁 GodotApp
                        isPresented = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            print("OfficialGodotGameView: 显示游戏视图（使用现有的 GodotApp）")
        }
        .onDisappear {
            print("OfficialGodotGameView: 隐藏游戏视图（保持 GodotApp 运行）")
        }
    }

}

#Preview {
    OfficialGodotGameView(
        godotApp: GodotApp(packFile: "test2.pck"),
        isPresented: .constant(true)
    )
}

//
//  MinimalGodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//  完全基于官方SwiftGodotKitSamples的最简实现
//

import SwiftUI
import SwiftGodotKit

struct MinimalGodotGameView: View {
    // 完全按照官方示例 - 直接在State中初始化
    @State var app: GodotApp = GodotApp(packFile: "test2.pck")
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            // 完全按照官方示例的结构
            VStack {
                Text("The game below is a Godot game:")
                    .foregroundColor(.white)
                    .padding(.top, 50)

                GodotAppView()
                    .background(Color.black)
            }
            .environment(\.godotApp, app)

            // 关闭按钮 - 放在右上角
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
    }
}

#Preview {
    MinimalGodotGameView(isPresented: .constant(true))
}

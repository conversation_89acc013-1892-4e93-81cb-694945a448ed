//
//  GlobalGodotManager.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//  全局Godot管理器 - 支持数据传入和动态控制
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

/// 全局Godot管理器 - 确保只有一个GodotApp实例
class GlobalGodotManager: ObservableObject {
    static let shared = GlobalGodotManager()
    
    @Published var currentApp: GodotApp?
    @Published var isReady = false
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private var hasInitialized = false
    private var currentPckFile: String?
    
    private init() {
        print("GlobalGodotManager: 初始化全局管理器")
    }
    
    /// 获取或创建GodotApp实例
    func getGodotApp(pckFile: String, gameData: [String: Any]? = nil) -> GodotApp? {
        // 如果已经有相同PCK的实例，直接返回
        if let existingApp = currentApp, currentPckFile == pckFile {
            print("GlobalGodotManager: 返回现有的GodotApp实例 (\(pckFile))")
            
            // 如果有新数据，传递给Godot
            if let data = gameData {
                sendDataToGodot(data: data)
            }
            
            return existingApp
        }
        
        // 如果PCK文件不同，需要重新初始化
        if currentPckFile != pckFile {
            print("GlobalGodotManager: PCK文件变更，重新初始化 (\(currentPckFile ?? "nil") -> \(pckFile))")
            resetEngine()
        }
        
        // 初始化新的GodotApp
        return initializeGodotApp(pckFile: pckFile, gameData: gameData)
    }
    
    private func initializeGodotApp(pckFile: String, gameData: [String: Any]? = nil) -> GodotApp? {
        guard !hasInitialized else {
            print("GlobalGodotManager: 已经在初始化中")
            return currentApp
        }
        
        // 检查PCK文件
        let pckName = pckFile.replacingOccurrences(of: ".pck", with: "")
        guard Bundle.main.path(forResource: pckName, ofType: "pck") != nil else {
            DispatchQueue.main.async {
                self.errorMessage = "PCK文件 '\(pckFile)' 未找到"
            }
            return nil
        }
        
        hasInitialized = true
        currentPckFile = pckFile
        
        DispatchQueue.main.async {
            self.isLoading = true
            self.errorMessage = nil
        }
        
        print("GlobalGodotManager: 开始初始化Godot引擎，PCK: \(pckFile)")
        
        do {
            let app = GodotApp(packFile: pckFile)
            
            DispatchQueue.main.async {
                self.currentApp = app
                
                // 延迟设置就绪状态，给引擎时间初始化
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.isReady = true
                    self.isLoading = false
                    print("GlobalGodotManager: Godot引擎就绪")
                    
                    // 如果有初始数据，传递给Godot
                    if let data = gameData {
                        self.sendDataToGodot(data: data)
                    }
                }
            }
            
            return app
            
        } catch {
            print("GlobalGodotManager: GodotApp创建失败: \(error)")
            DispatchQueue.main.async {
                self.errorMessage = "引擎创建失败: \(error.localizedDescription)"
                self.isLoading = false
                self.hasInitialized = false
            }
            return nil
        }
    }
    
    /// 向Godot传递数据
    private func sendDataToGodot(data: [String: Any]) {
        guard let app = currentApp, isReady else {
            print("GlobalGodotManager: Godot未就绪，无法传递数据")
            return
        }
        
        print("GlobalGodotManager: 向Godot传递数据: \(data)")
        
        // 这里可以通过Godot的信号系统或全局变量传递数据
        // 具体实现取决于您的Godot项目结构
        
        // 示例：通过Godot的全局变量传递数据
        // 您需要根据实际的Godot项目调整这部分代码
        /*
        for (key, value) in data {
            // 设置Godot全局变量
            // Engine.setGlobalVariable(key, value)
        }
        */
    }
    
    /// 更新游戏数据
    func updateGameData(_ data: [String: Any]) {
        sendDataToGodot(data: data)
    }
    
    /// 隐藏游戏（不销毁引擎）
    func hideGame() {
        print("GlobalGodotManager: 隐藏游戏（保持引擎运行）")
        // 保持引擎运行，只是隐藏视图
    }
    
    /// 重置引擎（谨慎使用）
    func resetEngine() {
        print("GlobalGodotManager: 重置引擎状态")
        currentApp = nil
        isReady = false
        isLoading = false
        hasInitialized = false
        currentPckFile = nil
        errorMessage = nil
    }
    
    /// 获取状态信息
    func getStatusInfo() -> String {
        var status = "Godot引擎状态:\n"
        status += "- PCK文件: \(currentPckFile ?? "无")\n"
        status += "- 已初始化: \(hasInitialized)\n"
        status += "- 引擎就绪: \(isReady)\n"
        status += "- 正在加载: \(isLoading)\n"
        status += "- 应用实例: \(currentApp != nil ? "存在" : "无")\n"
        if let error = errorMessage {
            status += "- 错误: \(error)\n"
        }
        return status
    }
}

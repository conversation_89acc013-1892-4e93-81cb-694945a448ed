//
//  SimpleGodotInteractionView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

/// 简化的Godot交互视图 - 展示嵌入式Godot控件交互
struct SimpleGodotInteractionView: View {
    let godotApp: GodotApp
    @Binding var isPresented: Bool
    @State private var interactionCount = 0

    var body: some View {
        ZStack {
            // 背景
            Color.black.opacity(0.9)
                .ignoresSafeArea()

            VStack(spacing: 20) {
                // 顶部标题栏
                topBar

                // 主要内容区域
                mainContent

                // 底部状态栏
                bottomStatusBar
            }
            .padding()
        }
    }

    @ViewBuilder
    private var topBar: some View {
        HStack {
            Text("Godot交互控制台")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)

            Spacer()

            Button(action: {
                isPresented = false
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(.white)
            }
        }
        .padding(.horizontal)
        .padding(.top, 10)
    }

    @ViewBuilder
    private var mainContent: some View {
        VStack(spacing: 20) {
            // 说明文字
            VStack(spacing: 8) {
                Text("嵌入式Godot控件演示")
                    .font(.headline)
                    .foregroundColor(.white)

                Text("下方是在Swift中创建的Godot UI控件，可以直接与PCK中的逻辑交互")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }

            // Godot嵌入式控件区域
            GodotWindow { host in
                createSimpleGodotContent(in: host)
            }
            .frame(height: 300)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(12)
            .environment(\.godotApp, godotApp)

            // Swift控制区域
            swiftControlArea
        }
    }

    @ViewBuilder
    private var swiftControlArea: some View {
        VStack(spacing: 16) {
            Text("Swift控制区域")
                .font(.headline)
                .foregroundColor(.white)

            VStack(spacing: 12) {
                // 状态显示
                HStack {
                    Text("当前交互计数:")
                        .foregroundColor(.white.opacity(0.8))
                    Spacer()
                    Text("\(interactionCount)")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }

                // 控制按钮组
                HStack(spacing: 16) {
                    Button("执行Demo操作") {
                        interactionCount += 1
                    }
                    .buttonStyle(InteractionButtonStyle(color: .blue))

                    Button("重置计数器") {
                        interactionCount = 0
                    }
                    .buttonStyle(InteractionButtonStyle(color: .red))
                }

                Button("批量操作 (10,000次)") {
                    interactionCount += 10000
                }
                .buttonStyle(InteractionButtonStyle(color: .orange))
                .frame(maxWidth: .infinity)
            }
            .padding()
            .background(Color.white.opacity(0.1))
            .cornerRadius(12)
        }
    }

    @ViewBuilder
    private var bottomStatusBar: some View {
        HStack {
            HStack {
                Circle()
                    .fill(Color.green)
                    .frame(width: 8, height: 8)

                Text("交互已启用")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }

            Spacer()

            Text("Godot + SwiftUI 混合交互")
                .font(.caption)
                .foregroundColor(.white.opacity(0.6))
        }
        .padding(.horizontal)
    }
}

/// 简化的Godot内容创建函数（参照示例代码）
@MainActor
func createSimpleGodotContent(in host: Node) {
    let container = SwiftGodot.VBoxContainer()
    container.setAnchorsPreset(SwiftGodot.Control.LayoutPreset.fullRect)
    host.addChild(node: container)

    var times = 0

    // 创建标题
    let titleLabel = SwiftGodot.Label()
    titleLabel.text = "Godot嵌入式控件"
    titleLabel.horizontalAlignment = SwiftGodot.HorizontalAlignment.center
    container.addChild(node: titleLabel)

    // 创建第一个按钮（参照示例）
    let button1 = SwiftGodot.Button()
    button1.text = "Godot Button"
    button1.pressed.connect {
        times += 1
        button1.text = "已点击 \(times) 次"

        // 执行Demo操作（参照示例代码）
        for _ in 0..<10000 {
            // 简化的Demo操作
            let _ = times * 2
        }

        print("Godot按钮被点击了 \(times) 次，执行了10000次Demo操作")
    }
    container.addChild(node: button1)

    // 创建第二个按钮
    let button2 = SwiftGodot.Button()
    button2.text = "另一个Godot按钮"
    button2.pressed.connect {
        print("第二个Godot按钮被点击")
        button2.text = "按钮已激活"
    }
    container.addChild(node: button2)

    // 创建状态显示标签
    let statusLabel = SwiftGodot.Label()
    statusLabel.text = "状态: 就绪"
    statusLabel.horizontalAlignment = SwiftGodot.HorizontalAlignment.center
    container.addChild(node: statusLabel)

    // 创建重置按钮
    let resetButton = SwiftGodot.Button()
    resetButton.text = "重置所有计数"
    resetButton.pressed.connect {
        times = 0
        button1.text = "Godot Button"
        button2.text = "另一个Godot按钮"
        statusLabel.text = "状态: 已重置"
        print("所有计数已重置")
    }
    container.addChild(node: resetButton)
}

/// 交互按钮样式
struct InteractionButtonStyle: ButtonStyle {
    let color: SwiftUI.Color

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.body)
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(color.opacity(configuration.isPressed ? 0.8 : 1.0))
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
    }
}

#Preview {
    // 预览需要模拟GodotApp
    SimpleGodotInteractionView(
        godotApp: GodotApp(packFile: "main.pck"),
        isPresented: .constant(true)
    )
}

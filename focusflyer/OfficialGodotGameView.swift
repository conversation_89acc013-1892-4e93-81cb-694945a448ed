//
//  OfficialGodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//  基于官方SwiftGodotKitSamples示例的实现
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct OfficialGodotGameView: View {
    // 完全按照官方示例的模式
    @State private var godotApp: GodotApp?
    @State private var errorMessage: String?
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if let godotApp = godotApp {
                // 完全按照官方示例的结构
                VStack {
                    Text("Godot Game Running")
                        .foregroundColor(.white)
                        .padding(.top, 50)
                        .font(.caption)
                    
                    GodotAppView()
                        .background(Color.black)
                }
                .environment(\.godotApp, godotApp)
            } else if let errorMessage = errorMessage {
                // 错误显示
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("Godot Error")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Button("Retry") {
                        loadGodotGame()
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
            } else {
                // 加载中
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("Loading Godot...")
                        .font(.title2)
                }
            }

            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            loadGodotGame()
        }
    }
    
    private func loadGodotGame() {
        print("Loading Godot game...")
        errorMessage = nil
        
        // 检查PCK文件
        guard Bundle.main.path(forResource: "test2", ofType: "pck") != nil else {
            errorMessage = "PCK file 'test2.pck' not found in app bundle"
            return
        }
        
        // 按照官方示例的方式直接创建GodotApp
        do {
            print("Creating GodotApp with test2.pck...")
            let app = GodotApp(packFile: "test2.pck")
            self.godotApp = app
            print("Successfully created GodotApp")
        } catch {
            print("Failed to create GodotApp: \(error)")
            errorMessage = "Failed to load Godot game: \(error.localizedDescription)"
        }
    }
}

#Preview {
    OfficialGodotGameView(isPresented: .constant(true))
}

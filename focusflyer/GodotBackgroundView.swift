//
//  GodotBackgroundView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit

/// Godot背景渲染视图 - 作为整个应用的底层渲染基础
struct GodotBackgroundView: View {
    @StateObject private var godotEngine = GlobalGodotEngine.shared
    
    var body: some View {
        ZStack {
            // Godot引擎渲染层 - 始终存在的底层
            if let godotApp = godotEngine.godotApp, case .ready = godotEngine.engineState {
                GodotAppView()
                    .environment(\.godotApp, godotApp)
                    .ignoresSafeArea(.all) // 全屏渲染
                    .allowsHitTesting(false) // 允许上层视图接收触摸事件
                
            } else if case .initializing = godotEngine.engineState {
                // 引擎初始化中的占位视图
                initializingView
                
            } else if case .error(let message) = godotEngine.engineState {
                // 引擎错误视图
                errorView(message: message)
            }
        }
    }
    
    @ViewBuilder
    private var initializingView: some View {
        ZStack {
            // 渐变背景作为初始化时的占位
            LinearGradient(
                gradient: Gradient(colors: [Color.blue.opacity(0.3), Color.purple.opacity(0.3)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea(.all)
            
            VStack(spacing: 20) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(.white)
                
                Text("正在启动Godot引擎...")
                    .font(.title2)
                    .foregroundColor(.white)
                
                Text("首次启动可能需要几秒钟")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
    }
    
    @ViewBuilder
    private func errorView(message: String) -> some View {
        ZStack {
            Color.red.opacity(0.2)
                .ignoresSafeArea(.all)
            
            VStack(spacing: 20) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: 50))
                    .foregroundColor(.red)
                
                Text("Godot引擎启动失败")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(message)
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                Button("重新启动引擎") {
                    godotEngine.reinitializeEngine()
                }
                .buttonStyle(.bordered)
                .tint(.red)
            }
            .foregroundColor(.primary)
            .padding()
        }
    }
}

#Preview {
    GodotBackgroundView()
}

//
//  RiveFloatingView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import RiveRuntime

/// Rive悬浮组件 - 右下角的可展开Rive演示
struct RiveFloatingView: View {
    @State private var isExpanded = false
    @State private var dragOffset = CGSize.zero
    private let riveVM = RiveViewModel(fileName: "spegni_il_cervello")
    
    var body: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                
                if isExpanded {
                    expandedRiveView
                } else {
                    collapsedRiveButton
                }
            }
        }
        .padding(.bottom, 100) // 避免与底部安全区域重叠
        .padding(.trailing, 20)
    }
    
    @ViewBuilder
    private var collapsedRiveButton: some View {
        Button(action: {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                isExpanded = true
            }
        }) {
            ZStack {
                Circle()
                    .fill(Color.black.opacity(0.7))
                    .frame(width: 80, height: 80)
                
                RiveViewRepresentable(model: riveVM)
                    .frame(width: 60, height: 60)
                    .clipShape(Circle())
            }
        }
        .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
        .offset(dragOffset)
        .gesture(
            DragGesture()
                .onChanged { value in
                    dragOffset = value.translation
                }
                .onEnded { value in
                    withAnimation(.spring()) {
                        dragOffset = .zero
                    }
                }
        )
    }
    
    @ViewBuilder
    private var expandedRiveView: some View {
        VStack(spacing: 0) {
            // 顶部控制栏
            HStack {
                Text("Rive 演示")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                        isExpanded = false
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }
            .padding()
            .background(Color.black.opacity(0.8))
            
            // Rive内容区域
            RiveViewRepresentable(model: riveVM)
                .aspectRatio(contentMode: .fit)
                .frame(width: 300, height: 300)
                .background(Color.black.opacity(0.9))
            
            // 底部信息栏
            VStack(spacing: 8) {
                Text("交互式Rive动画")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text("点击动画进行交互")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            .padding()
            .background(Color.black.opacity(0.8))
        }
        .cornerRadius(20)
        .shadow(color: .black.opacity(0.5), radius: 20, x: 0, y: 10)
        .transition(.asymmetric(
            insertion: .scale(scale: 0.1).combined(with: .opacity),
            removal: .scale(scale: 0.1).combined(with: .opacity)
        ))
    }
}

#Preview {
    ZStack {
        Color.blue.ignoresSafeArea()
        RiveFloatingView()
    }
}

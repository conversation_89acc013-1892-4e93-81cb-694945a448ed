//
//  PersistentGodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct PersistentGodotGameView: View {
    @StateObject private var godotEngine = GlobalGodotEngine.shared

    var body: some View {
        ZStack {
            // 主要内容
            if let godotApp = godotEngine.godotApp, case .ready = godotEngine.engineState {
                // Godot游戏视图
                VStack {
                    Text("Godot Game Running")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.top, 50)

                    GodotAppView()
                        .background(Color.black)
                }
                .environment(\.godotApp, godotApp)
                .ignoresSafeArea()

            } else if case .initializing = godotEngine.engineState {
                // 加载视图
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                        .tint(.white)

                    Text("正在加载Godot引擎...")
                        .font(.title2)
                        .foregroundColor(.white)

                    Text("首次加载可能需要几秒钟")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black)

            } else if case .error(let message) = godotEngine.engineState {
                // 错误视图
                VStack(spacing: 20) {
                    Image(systemName: "gamecontroller")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("Godot引擎加载失败")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text(message)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.white.opacity(0.8))
                        .padding(.horizontal)

                    VStack(spacing: 10) {
                        Button("重试") {
                            godotEngine.reinitializeEngine()
                        }
                        .buttonStyle(.bordered)
                        .tint(.blue)

                        Button("强制重新初始化") {
                            godotEngine.reinitializeEngine()
                        }
                        .buttonStyle(.bordered)
                        .tint(.orange)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black)
                .padding()

            } else {
                // 准备状态
                VStack(spacing: 20) {
                    Text("正在准备游戏...")
                        .font(.title2)
                        .foregroundColor(.white)

                    ProgressView()
                        .tint(.white)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black)
            }

            // 关闭按钮覆盖层
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        // 这个视图现在不再需要hideGame功能，因为它是独立的
                        // 可以添加其他功能或者移除这个按钮
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            // Godot引擎现在是自动初始化的，不需要手动调用
        }
        .navigationBarHidden(true)
    }
}

#Preview {
    PersistentGodotGameView()
}

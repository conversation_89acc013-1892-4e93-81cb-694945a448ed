//
//  ContentView.swift
//  focusflyer
//
//  Created by 张国豪 on 2025/7/1.
//

import RiveRuntime
import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct ContentView: View {
    private let riveVM = RiveViewModel(fileName: "spegni_il_cervello")
    @State private var showGodotGame = false
    @StateObject private var godotManager = GlobalGodotManager.shared

    var body: some View {
        VStack(spacing: 24) {
            RiveViewRepresentable(model: riveVM)
                .aspectRatio(contentMode: .fit)
                .frame(width: 300, height: 300)

            Button(action: {
                // 直接显示游戏视图，让视图自己处理初始化
                showGodotGame = true
            }) {
                HStack {
                    Image(systemName: "gamecontroller.fill")
                    Text("Launch Godot Game")
                }
                .font(.title2)
                .foregroundColor(.white)
                .padding()
                .background(Color.blue)
                .cornerRadius(10)
            }
        }
        .padding()
        .fullScreenCover(isPresented: $showGodotGame) {
            // 使用独立的游戏视图
            GodotGameView(isPresented: $showGodotGame)
        }
    }
}

#Preview {
  ContentView()
}

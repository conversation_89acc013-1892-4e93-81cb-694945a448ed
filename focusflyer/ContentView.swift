//
//  ContentView.swift
//  focusflyer
//
//  Created by 张国豪 on 2025/7/1.
//

import RiveRuntime
import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct ContentView: View {
    private let riveVM = RiveViewModel(fileName: "spegni_il_cervello")
    @StateObject private var pckManager = PCKManager.shared

    var body: some View {
        VStack(spacing: 24) {
            // Rive动画
            RiveViewRepresentable(model: riveVM)
                .aspectRatio(contentMode: .fit)
                .frame(width: 300, height: 300)

            // PCK状态显示
            statusView

            // 启动游戏按钮
            gameButton
        }
        .padding()
        .fullScreenCover(isPresented: $pckManager.isGameViewVisible) {
            PersistentGodotGameView()
        }
    }

    @ViewBuilder
    private var statusView: some View {
        VStack(spacing: 8) {
            switch pckManager.state {
            case .idle:
                Text("准备启动游戏...")
                    .foregroundColor(.secondary)
            case .loading:
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("正在加载Godot引擎...")
                }
                .foregroundColor(.blue)
            case .loaded:
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("Godot引擎已就绪")
                }
            case .error(let message):
                VStack {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                        Text("错误")
                    }
                    Text(message)
                        .font(.caption)
                        .multilineTextAlignment(.center)
                    Button("重试") {
                        pckManager.retry()
                    }
                    .buttonStyle(.bordered)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }

    @ViewBuilder
    private var gameButton: some View {
        Button(action: {
            pckManager.showGame()
        }) {
            HStack {
                Image(systemName: "gamecontroller.fill")
                Text("启动Godot游戏")
            }
            .font(.title2)
            .foregroundColor(.white)
            .padding()
            .background(pckManager.state == .loaded ? Color.green : Color.blue)
            .cornerRadius(10)
        }
        .disabled(pckManager.state == .loading)
    }
}

#Preview {
  ContentView()
}

//
//  ContentView.swift
//  focusflyer
//
//  Created by 张国豪 on 2025/7/1.
//

import RiveRuntime
import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct ContentView: View {
    private let riveVM = RiveViewModel(fileName: "spegni_il_cervello")
    @State private var showGodotGame = false
    @StateObject private var godotManager = GlobalGodotManager.shared

    var body: some View {
        VStack(spacing: 24) {
            RiveViewRepresentable(model: riveVM)
                .aspectRatio(contentMode: .fit)
                .frame(width: 300, height: 300)

            Button(action: {
                // 准备游戏数据
                let gameData: [String: Any] = [
                    "level": 1,
                    "playerName": "Player1",
                    "difficulty": "normal"
                ]

                // 通过管理器获取GodotApp实例
                if godotManager.getGodotApp(pckFile: "test2.pck", gameData: gameData) != nil {
                    showGodotGame = true
                } else {
                    print("无法启动Godot游戏")
                }
            }) {
                HStack {
                    Image(systemName: "gamecontroller.fill")
                    Text("Launch Godot Game")
                }
                .font(.title2)
                .foregroundColor(.white)
                .padding()
                .background(Color.blue)
                .cornerRadius(10)
            }
        }
        .padding()
        .fullScreenCover(isPresented: $showGodotGame) {
            // 使用全局管理器的游戏视图
            ManagedGodotGameView(isPresented: $showGodotGame)
        }
    }
}

#Preview {
  ContentView()
}

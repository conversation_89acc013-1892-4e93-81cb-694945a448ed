//
//  ContentView.swift
//  focusflyer
//
//  Created by 张国豪 on 2025/7/1.
//

import RiveRuntime
import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct ContentView: View {
    private let riveVM = RiveViewModel(fileName: "spegni_il_cervello")
    @StateObject private var pckManager = PCKManager()

    var body: some View {
        VStack(spacing: 24) {
            // Rive动画
            RiveViewRepresentable(model: riveVM)
                .aspectRatio(contentMode: .fit)
                .frame(width: 300, height: 300)

            // PCK状态显示
            statusView

            // PCK切换按钮
            pckButtonsView

            // Godot游戏视图
            if let godotApp = pckManager.currentApp, case .loaded = pckManager.state {
                godotGameView(app: godotApp)
            }
        }
        .padding()
    }

    @ViewBuilder
    private var statusView: some View {
        VStack(spacing: 8) {
            switch pckManager.state {
            case .idle:
                Text("准备中...")
                    .foregroundColor(.secondary)
            case .loading:
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("加载中...")
                }
                .foregroundColor(.blue)
            case .loaded:
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("当前PCK: \(pckManager.currentPCK ?? "未知")")
                }
            case .switching:
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("切换中...")
                }
                .foregroundColor(.orange)
            case .error(let message):
                VStack {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                        Text("错误")
                    }
                    Text(message)
                        .font(.caption)
                        .multilineTextAlignment(.center)
                    Button("重试") {
                        pckManager.retry()
                    }
                    .buttonStyle(.bordered)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }

    @ViewBuilder
    private var pckButtonsView: some View {
        HStack(spacing: 20) {
            Button(action: {
                pckManager.loadPCK("main.pck")
            }) {
                VStack {
                    Image(systemName: "gamecontroller")
                    Text("Main Game")
                }
                .font(.title2)
                .foregroundColor(.white)
                .padding()
                .background(pckManager.currentPCK == "main.pck" ? Color.green : Color.blue)
                .cornerRadius(10)
            }
            .disabled(pckManager.state == .loading || pckManager.state == .switching)

            Button(action: {
                pckManager.loadPCK("test2.pck")
            }) {
                VStack {
                    Image(systemName: "gamecontroller.fill")
                    Text("Test Game")
                }
                .font(.title2)
                .foregroundColor(.white)
                .padding()
                .background(pckManager.currentPCK == "test2.pck" ? Color.green : Color.orange)
                .cornerRadius(10)
            }
            .disabled(pckManager.state == .loading || pckManager.state == .switching)
        }
    }

    @ViewBuilder
    private func godotGameView(app: GodotApp) -> some View {
        VStack {
            Text("Godot Game Running")
                .font(.headline)
                .padding(.top)

            GodotAppView()
                .frame(height: 400)
                .background(Color.black)
                .cornerRadius(10)
                .environment(\.godotApp, app)
        }
    }
}

#Preview {
  ContentView()
}

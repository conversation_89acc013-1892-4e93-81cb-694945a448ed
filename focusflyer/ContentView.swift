//
//  ContentView.swift
//  focusflyer
//
//  Created by 张国豪 on 2025/7/1.
//

import RiveRuntime
import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct ContentView: View {
    private let riveVM = RiveViewModel(fileName: "spegni_il_cervello")
    @State var godotApp: GodotApp? = nil
    @State private var showGodotGame = false
    @State private var isInitializing = false
    @State private var errorMessage: String? = nil

    var body: some View {
        VStack(spacing: 24) {
            // Rive动画
            RiveViewRepresentable(model: riveVM)
                .aspectRatio(contentMode: .fit)
                .frame(width: 300, height: 300)

            // 状态显示
            statusView

            // 游戏控制按钮
            gameControlButtons

            // 嵌入式Godot内容（如果已初始化）
            if let app = godotApp {
                embeddedGodotContent(app: app)
            }
        }
        .padding()
        .fullScreenCover(isPresented: $showGodotGame) {
            if let app = godotApp {
                FullScreenGodotView(app: app, isPresented: $showGodotGame)
            }
        }
    }

    @ViewBuilder
    private var statusView: some View {
        VStack(spacing: 8) {
            if isInitializing {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("正在初始化Godot引擎...")
                }
                .foregroundColor(.blue)
            } else if let error = errorMessage {
                VStack {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                        Text("错误")
                    }
                    Text(error)
                        .font(.caption)
                        .multilineTextAlignment(.center)
                    Button("重试") {
                        initializeGodot()
                    }
                    .buttonStyle(.bordered)
                }
            } else if godotApp != nil {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("Godot引擎已就绪")
                }
            } else {
                Text("点击按钮初始化Godot引擎")
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }

    @ViewBuilder
    private var gameControlButtons: some View {
        VStack(spacing: 15) {
            // 初始化按钮
            if godotApp == nil {
                Button(action: {
                    initializeGodot()
                }) {
                    HStack {
                        Image(systemName: "power")
                        Text("初始化Godot引擎")
                    }
                    .font(.title2)
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(10)
                }
                .disabled(isInitializing)
            } else {
                // 游戏控制按钮
                HStack(spacing: 20) {
                    Button(action: {
                        showGodotGame = true
                    }) {
                        HStack {
                            Image(systemName: "gamecontroller.fill")
                            Text("全屏游戏")
                        }
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.green)
                        .cornerRadius(10)
                    }

                    Button(action: {
                        resetGodot()
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("重置引擎")
                        }
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.orange)
                        .cornerRadius(10)
                    }
                }
            }
        }
    }

    // MARK: - Godot控制函数
    private func initializeGodot() {
        guard !isInitializing else { return }

        isInitializing = true
        errorMessage = nil

        // 检查PCK文件
        guard Bundle.main.path(forResource: "main", ofType: "pck") != nil else {
            errorMessage = "main.pck文件未找到"
            isInitializing = false
            return
        }

        // 延迟初始化，确保UI准备就绪
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            do {
                print("ContentView: 创建GodotApp实例")
                let app = GodotApp(packFile: "main.pck")

                // 给Godot引擎时间初始化
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    self.godotApp = app
                    self.isInitializing = false
                    print("ContentView: Godot引擎初始化完成")
                }

            } catch {
                print("ContentView: Godot初始化失败 - \(error)")
                self.errorMessage = "初始化失败: \(error.localizedDescription)"
                self.isInitializing = false
            }
        }
    }

    private func resetGodot() {
        print("ContentView: 重置Godot引擎")
        godotApp = nil
        errorMessage = nil
        // 可以选择立即重新初始化或等待用户操作
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            initializeGodot()
        }
    }

    // MARK: - 嵌入式Godot内容
    @ViewBuilder
    private func embeddedGodotContent(app: GodotApp) -> some View {
        VStack {
            Text("嵌入式Godot内容:")
                .font(.headline)
                .padding(.top)

            // 参照示例的GodotWindow用法
            GodotWindow { host in
                createGodotContent(in: host)
            }
            .frame(height: 200)
            .background(Color.black)
            .cornerRadius(10)
            .environment(\.godotApp, app)
        }
    }
}

// MARK: - Godot内容创建函数（参照示例）
@MainActor
func createGodotContent(in host: Node) {
    let container = SwiftGodot.VBoxContainer()
    container.setAnchorsPreset(SwiftGodot.Control.LayoutPreset.fullRect)
    host.addChild(node: container)

    var tapCount = 0

    // 创建第一个按钮
    let button1 = SwiftGodot.Button()
    button1.text = "Swift控制的Godot按钮"
    button1.pressed.connect {
        tapCount += 1
        button1.text = "已点击 \(tapCount) 次"
        print("Godot按钮被点击了 \(tapCount) 次")
    }

    // 创建第二个按钮
    let button2 = SwiftGodot.Button()
    button2.text = "另一个Godot按钮"
    button2.pressed.connect {
        print("第二个按钮被点击")
        button2.text = "按钮已激活"
    }

    // 创建标签
    let label = SwiftGodot.Label()
    label.text = "这是在Swift中创建的Godot UI"

    // 添加到容器
    container.addChild(node: label)
    container.addChild(node: button1)
    container.addChild(node: button2)
}

// MARK: - 全屏Godot视图
struct FullScreenGodotView: View {
    let app: GodotApp
    @Binding var isPresented: Bool

    var body: some View {
        ZStack {
            // 全屏Godot游戏视图
            GodotAppView()
                .environment(\.godotApp, app)
                .ignoresSafeArea()

            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .navigationBarHidden(true)
    }
}

#Preview {
  ContentView()
}

//
//  ContentView.swift
//  focusflyer
//
//  Created by 张国豪 on 2025/7/1.
//

import RiveRuntime
import SwiftUI
import SwiftGodotKit
import SwiftGodot

/// 主视图 - 业界最佳实践架构
/// Godot引擎作为底层渲染基础，所有SwiftUI视图悬浮在上面
struct ContentView: View {
    @StateObject private var godotEngine = GlobalGodotEngine.shared

    var body: some View {
        ZStack {
            // 底层：Godot引擎渲染 - 始终存在
            GodotBackgroundView()

            // 悬浮层：SwiftUI界面组件
            overlayViews
        }
        .ignoresSafeArea(.all) // 全屏体验
    }

    @ViewBuilder
    private var overlayViews: some View {
        ZStack {
            // 左上角：悬浮控制面板
            FloatingControlPanel()

            // 右下角：Rive悬浮组件
            RiveFloatingView()

            // 中央：欢迎信息（仅在引擎就绪时显示）
            if case .ready = godotEngine.engineState {
                welcomeOverlay
            }
        }
    }

    @ViewBuilder
    private var welcomeOverlay: some View {
        VStack {
            Spacer()

            HStack {
                // 左下角欢迎信息
                VStack(alignment: .leading, spacing: 12) {
                    Text("FocusFlyer")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)

                    Text("Godot引擎驱动的沉浸式体验")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.9))
                        .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("• 左上角：场景控制面板")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.8))

                        Text("• 右下角：Rive交互演示")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(0.7))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.white.opacity(0.2), lineWidth: 1)
                        )
                )
                .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)

                Spacer()

                // 右侧留空，避免挡住Rive按钮
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: 120) // 为右下角Rive按钮留出空间
            }
        }
        .padding(.leading, 20)
        .padding(.bottom, 120) // 避免与底部安全区域重叠
        .onAppear {
            // 5秒后自动隐藏欢迎信息
            DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                withAnimation(.easeOut(duration: 1.0)) {
                    // 这里可以添加隐藏逻辑
                }
            }
        }
    }
}



#Preview {
  ContentView()
}

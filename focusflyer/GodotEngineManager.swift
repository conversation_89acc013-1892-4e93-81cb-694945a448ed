//
//  GodotEngineManager.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//  全局Godot引擎管理器 - 解决重复初始化问题
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

/// 全局Godot引擎管理器 - 确保引擎只初始化一次
class GodotEngineManager: ObservableObject {
    static let shared = GodotEngineManager()

    @Published var isEngineInitialized = false
    @Published var currentApp: GodotApp?
    @Published var errorMessage: String?

    private var initializationAttempted = false

    private init() {
        print("GodotEngineManager: 初始化管理器")
    }

    /// 获取或创建Godot应用实例
    func getGodotApp(pckFile: String) -> GodotApp? {
        // 如果已经有实例，直接返回
        if let existingApp = currentApp {
            print("GodotEngineManager: 返回现有的GodotApp实例")
            return existingApp
        }

        // 如果之前初始化失败过，不再尝试
        if initializationAttempted && !isEngineInitialized {
            print("GodotEngineManager: 之前初始化失败，不再尝试")
            return nil
        }

        // 检查PCK文件
        guard Bundle.main.path(forResource: pckFile.replacingOccurrences(of: ".pck", with: ""), ofType: "pck") != nil else {
            errorMessage = "PCK文件 '\(pckFile)' 未找到"
            return nil
        }

        // 首次初始化
        return initializeGodotApp(pckFile: pckFile)
    }

    private func initializeGodotApp(pckFile: String) -> GodotApp? {
        guard !initializationAttempted else {
            print("GodotEngineManager: 已经尝试过初始化")
            return currentApp
        }

        initializationAttempted = true
        errorMessage = nil

        print("GodotEngineManager: 开始初始化Godot引擎，PCK文件: \(pckFile)")

        // 确保在主线程上初始化
        guard Thread.isMainThread else {
            print("GodotEngineManager: 错误 - 必须在主线程上初始化Godot")
            DispatchQueue.main.async {
                self.errorMessage = "引擎必须在主线程上初始化"
            }
            return nil
        }

        // 添加更多安全检查
        let app: GodotApp
        do {
            print("GodotEngineManager: 创建GodotApp实例...")
            app = GodotApp(packFile: pckFile)
            print("GodotEngineManager: GodotApp实例创建成功")
        } catch {
            print("GodotEngineManager: GodotApp创建失败: \(error)")
            errorMessage = "引擎创建失败: \(error.localizedDescription)"
            isEngineInitialized = false
            initializationAttempted = false // 允许重试
            return nil
        }

        // 延迟设置，给引擎时间初始化
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.currentApp = app
            self.isEngineInitialized = true
            print("GodotEngineManager: Godot引擎初始化完成")
        }

        return app
    }

    /// 隐藏游戏（不销毁引擎）
    func hideGame() {
        print("GodotEngineManager: 隐藏游戏（保持引擎运行）")
        // 不设置 currentApp = nil，保持引擎运行
    }

    /// 完全重置引擎（谨慎使用）
    func resetEngine() {
        print("GodotEngineManager: 重置引擎状态")
        currentApp = nil
        isEngineInitialized = false
        initializationAttempted = false
        errorMessage = nil
    }

    /// 获取引擎状态信息
    func getStatusInfo() -> String {
        var status = "引擎状态:\n"
        status += "- 初始化尝试: \(initializationAttempted)\n"
        status += "- 引擎就绪: \(isEngineInitialized)\n"
        status += "- 应用实例: \(currentApp != nil ? "存在" : "无")\n"
        if let error = errorMessage {
            status += "- 错误: \(error)\n"
        }
        return status
    }
}

/// 使用全局管理器的Godot游戏视图
struct SingletonGodotGameView: View {
    @StateObject private var engineManager = GodotEngineManager.shared
    @State private var godotApp: GodotApp?
    @State private var isLoading = true
    @State private var showDebugInfo = false
    @Binding var isPresented: Bool

    private let pckFileName = "test2.pck"

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if let godotApp = godotApp {
                // Godot游戏视图
                VStack {
                    if showDebugInfo {
                        VStack {
                            Text("引擎状态: 运行中")
                                .font(.caption)
                                .foregroundColor(.green)
                            Button("隐藏调试信息") {
                                showDebugInfo = false
                            }
                            .font(.caption)
                        }
                        .padding(.top, 50)
                    }

                    GodotAppView()
                        .background(Color.black)
                        .onTapGesture(count: 3) {
                            showDebugInfo.toggle()
                        }
                }
                .environment(\.godotApp, godotApp)
            } else if let errorMessage = engineManager.errorMessage {
                // 错误视图
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("Godot引擎错误")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    if showDebugInfo {
                        Text(engineManager.getStatusInfo())
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    }

                    HStack {
                        Button("重试") {
                            loadGame()
                        }
                        .buttonStyle(.bordered)

                        Button("重置引擎") {
                            resetEngine()
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.red)
                    }

                    Button(showDebugInfo ? "隐藏调试" : "显示调试") {
                        showDebugInfo.toggle()
                    }
                    .font(.caption)
                }
                .padding()
            } else if isLoading {
                // 加载视图
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("加载Godot引擎...")
                        .font(.title2)
                }
            }

            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        closeView()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            loadGame()
        }
    }

    private func loadGame() {
        print("SingletonGodotGameView: 开始加载游戏")

        // 确保在主线程上执行
        guard Thread.isMainThread else {
            DispatchQueue.main.async {
                self.loadGame()
            }
            return
        }

        isLoading = true

        // 延迟一点让UI更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            do {
                if let app = self.engineManager.getGodotApp(pckFile: self.pckFileName) {
                    // 再次延迟确保引擎完全初始化
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        self.godotApp = app
                        self.isLoading = false
                        print("SingletonGodotGameView: 游戏加载成功")
                    }
                } else {
                    self.isLoading = false
                    print("SingletonGodotGameView: 游戏加载失败")
                }
            } catch {
                print("SingletonGodotGameView: 加载过程中出现异常: \(error)")
                self.isLoading = false
            }
        }
    }

    private func resetEngine() {
        print("SingletonGodotGameView: 用户请求重置引擎")
        godotApp = nil
        engineManager.resetEngine()
        loadGame()
    }

    private func closeView() {
        print("SingletonGodotGameView: 关闭视图")
        // 只隐藏游戏，不销毁引擎
        engineManager.hideGame()
        isPresented = false
    }
}

#Preview {
    SingletonGodotGameView(isPresented: .constant(true))
}

//
//  PCKManager.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import Foundation
import SwiftUI
import SwiftGodotKit

enum PCKState: Equatable {
    case idle
    case loading
    case loaded
    case switching
    case error(String)

    static func == (lhs: PCKState, rhs: PCKState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.loading, .loading), (.loaded, .loaded), (.switching, .switching):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

@MainActor
class PCKManager: ObservableObject {
    @Published var currentApp: GodotApp?
    @Published var currentPCK: String?
    @Published var state: PCKState = .idle

    private var isInitializing = false

    init() {
        // 默认加载第一个PCK
        loadPCK("main.pck")
    }

    func loadPCK(_ pckName: String) {
        // 防止重复加载同一个PCK
        if currentPCK == pckName && currentApp != nil {
            print("PCKManager: PCK \(pckName) 已经加载")
            return
        }

        // 防止并发操作
        guard !isInitializing else {
            print("PCKManager: 正在初始化中，忽略请求")
            return
        }

        isInitializing = true

        if currentApp != nil {
            // 需要切换PCK
            switchToPCK(pckName)
        } else {
            // 首次加载
            initializePCK(pckName)
        }
    }

    private func switchToPCK(_ pckName: String) {
        print("PCKManager: 开始切换到 \(pckName)")
        state = .switching

        // 第1步：销毁当前实例
        currentApp = nil
        currentPCK = nil

        // 第2步：等待销毁完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.initializePCK(pckName)
        }
    }

    private func initializePCK(_ pckName: String) {
        print("PCKManager: 开始初始化 \(pckName)")
        state = .loading

        // 检查PCK文件是否存在
        let pckBaseName = pckName.replacingOccurrences(of: ".pck", with: "")
        guard Bundle.main.path(forResource: pckBaseName, ofType: "pck") != nil else {
            print("PCKManager: PCK文件 \(pckName) 不存在")
            state = .error("PCK文件 '\(pckName)' 未找到")
            isInitializing = false
            return
        }

        // 延迟初始化，确保UI准备就绪
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            do {
                print("PCKManager: 创建GodotApp实例 - \(pckName)")
                let app = GodotApp(packFile: pckName)

                // 给Godot引擎时间初始化
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    self.currentApp = app
                    self.currentPCK = pckName
                    self.state = .loaded
                    self.isInitializing = false
                    print("PCKManager: \(pckName) 加载完成")
                }

            } catch {
                print("PCKManager: 初始化失败 - \(error)")
                self.state = .error("初始化失败: \(error.localizedDescription)")
                self.isInitializing = false
            }
        }
    }

    func retry() {
        guard case .error = state else { return }
        if let pck = currentPCK {
            loadPCK(pck)
        } else {
            loadPCK("main.pck") // 默认重试main.pck
        }
    }
}

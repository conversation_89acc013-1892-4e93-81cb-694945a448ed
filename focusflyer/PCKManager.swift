//
//  PCKManager.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import Foundation
import SwiftUI
import SwiftGodotKit

enum PCKState: Equatable {
    case idle
    case loading
    case loaded
    case error(String)

    static func == (lhs: PCKState, rhs: PCKState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.loading, .loading), (.loaded, .loaded):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

@MainActor
class PCKManager: ObservableObject {
    static let shared = PCKManager()

    @Published var currentApp: GodotApp?
    @Published var state: PCKState = .idle
    @Published var isGameViewVisible = false

    private var isInitializing = false
    private let pckFileName = "main.pck"

    private init() {
        // 私有初始化，确保单例
    }

    func initializeIfNeeded() {
        guard currentApp == nil && !isInitializing else {
            print("PCKManager: 已经初始化或正在初始化中")
            return
        }

        initializeMainPCK()
    }

    func showGame() {
        initializeIfNeeded()
        isGameViewVisible = true
    }

    func hideGame() {
        isGameViewVisible = false
        // 注意：不销毁GodotApp，保持持久化
    }

    private func initializeMainPCK() {
        guard !isInitializing else { return }

        // 必须在主线程上执行
        guard Thread.isMainThread else {
            DispatchQueue.main.async {
                self.initializeMainPCK()
            }
            return
        }

        isInitializing = true
        state = .loading

        print("PCKManager: 开始初始化 \(pckFileName)")

        // 检查PCK文件是否存在
        let pckBaseName = pckFileName.replacingOccurrences(of: ".pck", with: "")
        guard Bundle.main.path(forResource: pckBaseName, ofType: "pck") != nil else {
            print("PCKManager: PCK文件 \(pckFileName) 不存在")
            state = .error("PCK文件 '\(pckFileName)' 未找到")
            isInitializing = false
            return
        }

        // 延迟初始化，确保UI准备就绪
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            do {
                print("PCKManager: 创建GodotApp实例 - \(self.pckFileName)")
                let app = GodotApp(packFile: self.pckFileName)

                // 给Godot引擎时间初始化
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    self.currentApp = app
                    self.state = .loaded
                    self.isInitializing = false
                    print("PCKManager: \(self.pckFileName) 加载完成并保持持久化")
                }

            } catch {
                print("PCKManager: 初始化失败 - \(error)")
                self.state = .error("初始化失败: \(error.localizedDescription)")
                self.isInitializing = false
            }
        }
    }

    func retry() {
        guard case .error = state else { return }
        currentApp = nil
        isInitializing = false
        initializeMainPCK()
    }

    // 强制重新初始化（仅在必要时使用）
    func forceReinitialize() {
        print("PCKManager: 强制重新初始化")
        currentApp = nil
        isInitializing = false
        state = .idle
        initializeMainPCK()
    }
}

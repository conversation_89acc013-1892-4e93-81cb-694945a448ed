//
//  PCKManager.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import Foundation
import SwiftUI
import SwiftGodotKit

enum GodotEngineState: Equatable {
    case initializing
    case ready
    case error(String)

    static func == (lhs: GodotEngineState, rhs: GodotEngineState) -> <PERSON><PERSON> {
        switch (lhs, rhs) {
        case (.initializing, .initializing), (.ready, .ready):
            return true
        case (.error(let lhsMessage), (.error(let rhsMessage))):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

/// 全局Godot引擎管理器 - 业界最佳实践
/// Godot引擎作为底层渲染基础，所有SwiftUI视图悬浮在上面
@MainActor
class GlobalGodotEngine: ObservableObject {
    static let shared = GlobalGodotEngine()

    @Published var godotApp: GodotApp?
    @Published var engineState: GodotEngineState = .initializing

    private let backgroundPCK = "main.pck"
    private var hasStartedInitialization = false

    private init() {
        // 应用启动时自动初始化Godot引擎
        autoInitializeEngine()
    }

    /// 自动初始化Godot引擎 - 应用启动时调用
    private func autoInitializeEngine() {
        guard !hasStartedInitialization else { return }
        hasStartedInitialization = true

        print("GlobalGodotEngine: 开始自动初始化Godot引擎")
        engineState = .initializing

        // 检查PCK文件
        let pckBaseName = backgroundPCK.replacingOccurrences(of: ".pck", with: "")
        guard Bundle.main.path(forResource: pckBaseName, ofType: "pck") != nil else {
            print("GlobalGodotEngine: 背景PCK文件 \(backgroundPCK) 不存在")
            engineState = .error("背景PCK文件未找到")
            return
        }

        // 延迟初始化，确保应用完全启动
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.initializeGodotEngine()
        }
    }

    private func initializeGodotEngine() {
        do {
            print("GlobalGodotEngine: 创建全局GodotApp实例")
            let app = GodotApp(packFile: backgroundPCK)

            // 给Godot引擎充足时间初始化
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                self.godotApp = app
                self.engineState = .ready
                print("GlobalGodotEngine: Godot引擎就绪，作为全局背景渲染")
            }

        } catch {
            print("GlobalGodotEngine: 引擎初始化失败 - \(error)")
            engineState = .error("引擎初始化失败: \(error.localizedDescription)")
        }
    }

    /// 切换场景/地图 - 相当于在同一个引擎中切换内容
    func switchScene(to sceneName: String) {
        print("GlobalGodotEngine: 切换场景到 \(sceneName)")
        // 这里可以通过Godot的信号系统或全局变量来切换场景
        // 而不需要重新初始化整个引擎
    }

    /// 重新初始化引擎（仅在出错时使用）
    func reinitializeEngine() {
        print("GlobalGodotEngine: 重新初始化引擎")
        godotApp = nil
        engineState = .initializing
        hasStartedInitialization = false
        autoInitializeEngine()
    }
}

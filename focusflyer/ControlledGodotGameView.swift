//
//  ControlledGodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//  结合官方示例和用户控制PCK加载的思路
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

// 全局单例管理Godot引擎状态
class GodotEngineManager: ObservableObject {
    static let shared = GodotEngineManager()
    
    @Published var isEngineInitialized = false
    @Published var currentApp: GodotApp?
    
    private init() {}
    
    func initializeEngine() {
        guard !isEngineInitialized else { return }
        
        print("Initializing Godot engine...")
        // 引擎会在第一次创建GodotApp时自动初始化
        isEngineInitialized = true
        print("Godot engine ready for PCK loading")
    }
    
    func loadPCK(filename: String) -> GodotApp? {
        guard isEngineInitialized else {
            print("Engine not initialized")
            return nil
        }
        
        guard Bundle.main.path(forResource: filename, ofType: "pck") != nil else {
            print("PCK file '\(filename).pck' not found")
            return nil
        }
        
        do {
            print("Loading PCK: \(filename).pck")
            let app = GodotApp(packFile: "\(filename).pck")
            currentApp = app
            return app
        } catch {
            print("Failed to load PCK: \(error)")
            return nil
        }
    }
    
    func unloadPCK() {
        print("Unloading current PCK")
        currentApp = nil
    }
}

struct ControlledGodotGameView: View {
    @StateObject private var engineManager = GodotEngineManager.shared
    @State private var godotApp: GodotApp?
    @State private var errorMessage: String?
    @State private var showGameControls = false
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if let godotApp = godotApp {
                // 游戏运行中
                VStack {
                    if showGameControls {
                        HStack {
                            Text("Godot Game Active")
                                .foregroundColor(.white)
                                .font(.caption)
                            Spacer()
                            Button("Hide Controls") {
                                showGameControls = false
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                        .padding(.horizontal)
                        .padding(.top, 50)
                    }
                    
                    GodotAppView()
                        .background(Color.black)
                        .onTapGesture(count: 2) {
                            showGameControls.toggle()
                        }
                }
                .environment(\.godotApp, godotApp)
            } else if let errorMessage = errorMessage {
                // 错误状态
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("Error")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Button("Retry") {
                        loadGame()
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
            } else if engineManager.isEngineInitialized {
                // 引擎就绪，等待加载游戏
                VStack(spacing: 30) {
                    Image(systemName: "gamecontroller")
                        .font(.system(size: 60))
                        .foregroundColor(.green)
                    
                    Text("Godot Engine Ready")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Engine initialized successfully.\nReady to load game content.")
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.secondary)
                    
                    Button("Load Game") {
                        loadGame()
                    }
                    .buttonStyle(.borderedProminent)
                    .font(.title2)
                    .padding()
                }
                .padding()
            } else {
                // 初始化引擎中
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("Initializing Godot Engine...")
                        .font(.title2)
                }
            }

            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        closeView()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            initializeIfNeeded()
        }
    }
    
    private func initializeIfNeeded() {
        if !engineManager.isEngineInitialized {
            // 延迟一点时间让UI显示初始化状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                engineManager.initializeEngine()
            }
        }
    }
    
    private func loadGame() {
        print("Loading game...")
        errorMessage = nil
        
        if let app = engineManager.loadPCK(filename: "test2") {
            self.godotApp = app
            print("Game loaded successfully")
        } else {
            errorMessage = "Failed to load game. Please check that test2.pck exists and is valid."
        }
    }
    
    private func closeView() {
        print("Closing Godot view...")
        
        // 不卸载引擎，只是停止显示游戏
        godotApp = nil
        showGameControls = false
        isPresented = false
    }
}

#Preview {
    ControlledGodotGameView(isPresented: .constant(true))
}

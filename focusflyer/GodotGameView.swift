//
//  GodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct GodotGameView: View {
    @State private var godotApp: GodotApp?
    @State private var errorMessage: String?
    @State private var isLoading = true
    @State private var isInitialized = false
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if let godotApp = godotApp {
                // Godot game view - following the demo pattern
                VStack {
                    Text("Godot Game Running")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.top, 50)

                    GodotAppView()
                        .background(Color.black)
                }
                .environment(\.godotApp, godotApp)
            } else if let errorMessage = errorMessage {
                // Error view
                VStack(spacing: 20) {
                    Image(systemName: "gamecontroller")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("Godot Game Error")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    VStack(spacing: 10) {
                        Text("To add a Godot game:")
                            .font(.headline)

                        VStack(alignment: .leading, spacing: 5) {
                            Text("1. Create a project in Godot Editor")
                            Text("2. Export as iOS .pck file")
                            Text("3. Replace test2.pck in the project")
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)

                    Button("Try Again") {
                        loadGodotGame()
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
            } else if isLoading {
                // Loading view
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)

                    Text("Loading Godot Game...")
                        .font(.title2)
                }
            }

            // Close button overlay
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        cleanupAndClose()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            if !isInitialized {
                loadGodotGame()
            }
        }
        .onDisappear {
            // Don't cleanup on disappear, only when explicitly closing
        }
    }

    private func loadGodotGame() {
        // Prevent multiple initializations
        guard !isInitialized else { return }

        // 必须在主线程上执行
        guard Thread.isMainThread else {
            print("GodotGameView: 错误 - 必须在主线程上初始化")
            DispatchQueue.main.async {
                self.loadGodotGame()
            }
            return
        }

        print("Loading Godot game...")
        isLoading = true
        errorMessage = nil
        isInitialized = true

        // Check if the pck file exists
        guard let pckPath = Bundle.main.path(forResource: "test2", ofType: "pck") else {
            print("Game file 'test2.pck' not found in app bundle")
            errorMessage = "No valid Godot game file found. Please add a valid .pck file to the project."
            isLoading = false
            isInitialized = false
            return
        }

        let fileSize = getFileSize(path: pckPath)
        print("Found pck file at: \(pckPath)")
        print("File size: \(fileSize) bytes")

        // Check if file is too small (likely empty or invalid)
        if fileSize < 100 {
            print("Warning: PCK file seems very small (\(fileSize) bytes), might be empty or invalid")
            // Instead of showing error, show a placeholder
            showPlaceholderGame()
            return
        }

        // 延迟初始化，确保UI已经完全准备好
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            do {
                print("Creating GodotApp instance...")

                // 在try-catch块中创建GodotApp
                let app = GodotApp(packFile: "test2.pck")
                print("GodotApp created successfully")

                // 给Godot更多时间初始化和加载主场景
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    self.godotApp = app
                    self.isLoading = false
                    print("Successfully loaded Godot game")
                    print("If you see this message but no game content, the PCK file might not contain a valid main scene")
                }
            } catch {
                print("Failed to initialize GodotApp: \(error)")
                self.errorMessage = "Failed to load Godot game: \(error.localizedDescription)"
                self.isLoading = false
                self.isInitialized = false
            }
        }
    }

    private func getFileSize(path: String) -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }

    private func showPlaceholderGame() {
        print("Showing placeholder game due to invalid PCK file")
        isLoading = false
        // Create a minimal placeholder - we'll show a message instead of Godot
        errorMessage = """
        PCK file is too small (\(getFileSize(path: Bundle.main.path(forResource: "test2", ofType: "pck") ?? "")) bytes).

        To fix this:
        1. Create a project in Godot Editor
        2. Add some content (scenes, sprites, etc.)
        3. Export as iOS PCK file
        4. Replace test2.pck in the project

        Current file size suggests an empty or invalid Godot project.
        """
    }

    private func cleanupAndClose() {
        print("Cleaning up Godot before closing...")

        // Set godotApp to nil to trigger cleanup
        godotApp = nil

        // Reset state
        isInitialized = false
        isLoading = true
        errorMessage = nil

        // Give time for cleanup before closing
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.isPresented = false
        }
    }
}

#Preview {
    GodotGameView(isPresented: .constant(true))
}

//
//  FloatingControlPanel.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

/// 悬浮控制面板 - 提供场景切换和应用控制功能
struct FloatingControlPanel: View {
    @StateObject private var godotEngine = GlobalGodotEngine.shared
    @State private var isExpanded = false
    @State private var selectedScene = "主场景"
    @State private var showInteractionPanel = false
    @State private var interactionCount = 0

    private let scenes = ["主场景", "游戏场景", "设置场景", "关于场景"]

    var body: some View {
        VStack {
            HStack {
                if isExpanded {
                    expandedControlPanel
                } else {
                    collapsedControlButton
                }
                Spacer()
            }
            Spacer()
        }
        .padding(.top, 60) // 避免与状态栏重叠
        .padding(.leading, 20)
    }

    @ViewBuilder
    private var collapsedControlButton: some View {
        Button(action: {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                isExpanded = true
            }
        }) {
            HStack(spacing: 8) {
                Image(systemName: "gamecontroller")
                    .font(.title2)
                Text("控制")
                    .font(.headline)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                Capsule()
                    .fill(Color.black.opacity(0.7))
                    .overlay(
                        Capsule()
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)
    }

    @ViewBuilder
    private var expandedControlPanel: some View {
        VStack(spacing: 16) {
            // 标题栏
            HStack {
                Text("场景控制")
                    .font(.headline)
                    .foregroundColor(.white)

                Spacer()

                Button(action: {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                        isExpanded = false
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white.opacity(0.8))
                }
            }

            Divider()
                .background(Color.white.opacity(0.3))

            // 引擎状态
            engineStatusView

            Divider()
                .background(Color.white.opacity(0.3))

            // 场景切换
            sceneControlView

            Divider()
                .background(Color.white.opacity(0.3))

            // 快捷操作
            quickActionsView

            Divider()
                .background(Color.white.opacity(0.3))

            // Godot交互控制
            godotInteractionView
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .frame(width: 320) // 增加宽度以容纳更多内容
        .shadow(color: .black.opacity(0.4), radius: 15, x: 0, y: 8)
        .transition(.asymmetric(
            insertion: .scale(scale: 0.8).combined(with: .opacity),
            removal: .scale(scale: 0.8).combined(with: .opacity)
        ))
        .fullScreenCover(isPresented: $showInteractionPanel) {
            if let godotApp = godotEngine.godotApp {
                SimpleGodotInteractionView(
                    godotApp: godotApp,
                    isPresented: $showInteractionPanel
                )
            }
        }
    }

    @ViewBuilder
    private var engineStatusView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("引擎状态")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)

            HStack {
                switch godotEngine.engineState {
                case .initializing:
                    ProgressView()
                        .scaleEffect(0.8)
                        .tint(.blue)
                    Text("初始化中...")
                        .foregroundColor(.blue)
                case .ready:
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("引擎就绪")
                        .foregroundColor(.green)
                case .error(let message):
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                    Text("错误")
                        .foregroundColor(.red)
                }
                Spacer()
            }
            .font(.caption)
        }
    }

    @ViewBuilder
    private var sceneControlView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("场景切换")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                ForEach(scenes, id: \.self) { scene in
                    Button(action: {
                        selectedScene = scene
                        godotEngine.switchScene(to: scene)
                    }) {
                        Text(scene)
                            .font(.caption)
                            .foregroundColor(selectedScene == scene ? .black : .white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(selectedScene == scene ? Color.white : Color.white.opacity(0.2))
                            )
                    }
                }
            }
        }
    }

    @ViewBuilder
    private var quickActionsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("快捷操作")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)

            HStack(spacing: 12) {
                Button("重启引擎") {
                    godotEngine.reinitializeEngine()
                }
                .buttonStyle(QuickActionButtonStyle(color: .orange))

                Button("全屏模式") {
                    // 切换全屏模式的逻辑
                }
                .buttonStyle(QuickActionButtonStyle(color: .blue))
            }
        }
    }

    @ViewBuilder
    private var godotInteractionView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Godot交互控制")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)

            VStack(spacing: 8) {
                // 交互状态显示
                HStack {
                    Text("交互计数:")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    Spacer()
                    Text("\(interactionCount)")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }

                // 交互控制按钮
                HStack(spacing: 8) {
                    Button("Demo操作") {
                        interactionCount += 1
                        showInteractionPanel = true
                    }
                    .buttonStyle(QuickActionButtonStyle(color: .purple))

                    Button("重置") {
                        interactionCount = 0
                    }
                    .buttonStyle(QuickActionButtonStyle(color: .red))
                }

                // 批量操作按钮
                Button("批量操作 (10K)") {
                    showInteractionPanel = true
                }
                .buttonStyle(QuickActionButtonStyle(color: .orange))
                .frame(maxWidth: .infinity)
            }
        }
    }
}

/// 快捷操作按钮样式
struct QuickActionButtonStyle: ButtonStyle {
    let color: SwiftUI.Color

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.caption)
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(color.opacity(configuration.isPressed ? 0.8 : 0.6))
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
    }
}

#Preview {
    ZStack {
        Color.blue.ignoresSafeArea()
        FloatingControlPanel()
    }
}
